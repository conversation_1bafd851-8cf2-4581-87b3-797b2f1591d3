<!--
 * @Description: 资助情况
 * @Autor: panmy
 * @Date: 2025-03-24 11:14:33
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-25 09:55:05
-->
<template>
  <a-card class="portal-card-box portal-notice-box">
    <template #title v-if="activeData.title">
      <CardHeader :title="activeData.title" :card="activeData.card" />
    </template>
    <div class="knbz">
      <div class="knbz_item knbz_item1">
        <img src="../image/zzqk_knbzzl.png" alt="" />
        <div>
          <p class="value">困难</p>
          <p class="name">困难生等级</p>
        </div>
      </div>
      <div class="knbz_item knbz_item2">
        <img src="../image/zzqk_knbzszr.png" alt="" />
        <div>
          <p class="value">0.8</p>
          <p class="name">本学年资助总额(万)</p>
        </div>
      </div>
      <div class="knbz_item knbz_item3">
        <img src="../image/zzqk_knbzze.png" alt="" />
        <div>
          <p class="value">19.6</p>
          <p class="name">专业平均资助金额(万)</p>
        </div>
      </div>
    </div>
    <div class="h-190px">
      <ScrollContainer>
        <div class="award-details">
          <div class="award-details-item" v-for="award in awards" :key="award.id" :class="[`state${award.status}`]">
            <div class="award-type">
              <div>{{ award.category }}</div>
              <div class="award-amount">¥{{ award.amount }}</div>
            </div>
            <div class="award-year">{{ award.academic_year }}</div>
          </div>
        </div>
      </ScrollContainer>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import useAsyncEcharts from './useAsyncEcharts.vue';
  import { ScrollContainer } from '@/components/Container';
  import { Empty } from 'ant-design-vue';
  import CardHeader from '@/components/VisualPortal/Portal/CardHeader/index.vue';
  const props = defineProps(['activeData']);

  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const awards = [
    {
      id: 1,
      student_id: 'ST1001',
      category: '助学金及困难生',
      funding_type: '国家助学金',
      amount: 3000,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 2,
      student_id: 'ST1002',
      category: '助学金及困难生',
      funding_type: '校级困难生',
      amount: 2000,
      academic_year: '2023-2024',
      status: 1,
    },
    {
      id: 3,
      student_id: 'ST1003',
      category: '助学金及困难生',
      funding_type: '社会资助',
      amount: 4000,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 4,
      student_id: 'ST1004',
      category: '贷款及勤工助学',
      funding_type: '国家助学贷款',
      amount: 8000,
      academic_year: '2023-2024',
      status: 3,
    },
    {
      id: 5,
      student_id: 'ST1005',
      category: '贷款及勤工助学',
      funding_type: '勤工助学',
      amount: 1500,
      academic_year: '2023-2024',
      status: 2,
    },
    {
      id: 6,
      student_id: 'ST1006',
      category: '贷款及勤工助学',
      funding_type: '企业贷款',
      amount: 12000,
      academic_year: '2023-2024',
      status: 3,
    },
    {
      id: 7,
      student_id: 'ST1007',
      category: '助学金及困难生',
      funding_type: '校友资助',
      amount: 5000,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 8,
      student_id: 'ST1008',
      category: '助学金及困难生',
      funding_type: '地方政府补助',
      amount: 3500,
      academic_year: '2023-2024',
      status: 1,
    },
    {
      id: 9,
      student_id: 'ST1009',
      category: '贷款及勤工助学',
      funding_type: '私人贷款',
      amount: 7000,
      academic_year: '2023-2024',
      status: 3,
    },
    {
      id: 10,
      student_id: 'ST1010',
      category: '贷款及勤工助学',
      funding_type: '暑期工读',
      amount: 2500,
      academic_year: '2023-2024',
      status: 2,
    },
    {
      id: 11,
      student_id: 'ST1011',
      category: '助学金及困难生',
      funding_type: '紧急救助金',
      amount: 1000,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 12,
      student_id: 'ST1012',
      category: '助学金及困难生',
      funding_type: '特殊困难生',
      amount: 4500,
      academic_year: '2023-2024',
      status: 1,
    },
    {
      id: 13,
      student_id: 'ST1013',
      category: '贷款及勤工助学',
      funding_type: '教育贷款',
      amount: 9000,
      academic_year: '2023-2024',
      status: 3,
    },
    {
      id: 14,
      student_id: 'ST1014',
      category: '贷款及勤工助学',
      funding_type: '兼职工作',
      amount: 2000,
      academic_year: '2023-2024',
      status: 2,
    },
    {
      id: 15,
      student_id: 'ST1015',
      category: '助学金及困难生',
      funding_type: '慈善机构资助',
      amount: 6000,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 16,
      student_id: 'ST1016',
      category: '助学金及困难生',
      funding_type: '奖学金',
      amount: 5000,
      academic_year: '2023-2024',
      status: 1,
    },
    {
      id: 17,
      student_id: 'ST1017',
      category: '贷款及勤工助学',
      funding_type: '合作企业资助',
      amount: 10000,
      academic_year: '2023-2024',
      status: 3,
    },
    {
      id: 18,
      student_id: 'ST1018',
      category: '贷款及勤工助学',
      funding_type: '校园兼职',
      amount: 1800,
      academic_year: '2023-2024',
      status: 2,
    },
    {
      id: 19,
      student_id: 'ST1019',
      category: '助学金及困难生',
      funding_type: '专项资助',
      amount: 3200,
      academic_year: '2023-2024',
      status: 4,
    },
    {
      id: 20,
      student_id: 'ST1020',
      category: '助学金及困难生',
      funding_type: '家庭经济困难生',
      amount: 2800,
      academic_year: '2023-2024',
      status: 1,
    },
  ];
</script>

<style lang="less" scoped>
  :deep(.ant-card-body) {
    padding: 20px !important;
  }
  .chart-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px;
    align-items: center;
    justify-content: space-between;
  }
  .chart-item {
    width: 30%;
  }
  .chart {
    height: 300px;
  }
  .button-group {
    display: flex;
    align-items: center;
  }

  .knbz {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 35px;
    margin-bottom: 15px;
    .knbz_item {
      border-radius: var(--border-radius);
      position: relative;
      display: flex;
      align-items: center;
      img {
        display: block;
        width: 60px;
        height: 60px;
        margin: 20px 15px 20px;
      }
      .value {
        font-size: 32px;
        font-weight: 900;
        line-height: 32px;
        color: rgba(255, 255, 255, 1);
        letter-spacing: 1px;
      }
      .name {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
        margin-top: 2px;
      }
    }
    .knbz_item1 {
      background: #04bdaa url('../image/knbz_qs.png') no-repeat right center;
    }
    .knbz_item2 {
      background: #ffc143 url('../image/knbz_yell.png') no-repeat right center;
    }
    .knbz_item3 {
      background: #ff6860 url('../image/knbz_red.png') no-repeat right center;
    }
  }

  .award-details {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-gap: 10px;
    justify-content: space-between;
    align-items: center;
    .award-details-item {
      height: 90px;
      padding: 8px 16px;
      border: 1px solid #eee;
      border-radius: 4px;
    }
  }

  .award-type {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
  }

  .award-amount {
    color: #ff4d4f; /* 红色 */
  }

  .award-year {
    color: #999;
  }

  .award-comment {
    margin-top: 16px;
    font-size: 16px;
    color: #333;
  }

  .edge-module {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-column-gap: 35px;
    margin-bottom: 15px;

    .edge-module_item {
      height: 80px;
      border-radius: var(--border-radius);
      display: flex;
      align-items: center;
      overflow: hidden;
      .value {
        font-size: 22px;
        font-weight: 900;
        letter-spacing: 1px;
        line-height: 32px;
      }
      .name {
        margin-top: 6px;
        font-size: 16px;
        color: rgba(144, 151, 174, 1);
      }
    }
    .edge-module_item1 {
      background: linear-gradient(90deg, rgba(70, 113, 253, 0.12) 0%, rgba(70, 113, 253, 0) 100%);
      &::before {
        content: '';
        display: block;
        background-color: #4671fd;
        width: 5px;
        height: 100%;
        margin-right: 15px;
      }
    }
    .edge-module_item2 {
      background: linear-gradient(270deg, rgba(116, 236, 247, 0) 0%, rgba(64, 213, 236, 0.12) 100%);
      &::before {
        content: '';
        display: block;
        background-color: #40d5ec;
        width: 5px;
        height: 100%;
        margin-right: 15px;
      }
    }
    .edge-module_item3 {
      background: linear-gradient(270deg, rgba(204, 182, 255, 0.05) 0%, rgba(157, 127, 254, 0.12) 100%);
      &::before {
        content: '';
        display: block;
        background-color: #9d7ffe;
        width: 5px;
        height: 100%;
        margin-right: 15px;
      }
    }
    .edge-module_item4 {
      background: linear-gradient(266deg, rgba(251, 172, 165, 0) 0%, rgba(246, 116, 108, 0.12) 100%);
      &::before {
        content: '';
        display: block;
        background-color: #ff6860;
        width: 5px;
        height: 100%;
        margin-right: 15px;
      }
    }
  }

  .state1 {
    background: url('../image/icon-status1.png') no-repeat right bottom/ 64px 48px;
  }
  .state2 {
    background: url('../image/icon-status2.png') no-repeat right bottom/ 64px 48px;
  }
  .state3 {
    background: url('../image/icon-status3.png') no-repeat right bottom/ 64px 48px;
  }
  .state4 {
    background: url('../image/icon-status4.png') no-repeat right bottom/ 64px 48px;
  }
</style>
