<!--
 * @Description: 学生资助情况 
 * @Autor: panmy
 * @Date: 2025-02-10 14:09:25
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-05 10:20:54
-->

<template>
  <div class="card xgpt-sxzzqk">
    <a-tabs v-model:activeKey="activeKey">
      <template #leftExtra>
        <p class="card-title">学生资助情况</p>
      </template>
      <a-tab-pane key="1" tab="资助工作概述" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">当年资助情况概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="two-column-grid-with-gap">
            <use-async-echarts-and-detail :loading="false" :logo="disadvantagedStudents.logo" :options="disadvantagedStudents.options" height="250px" />
            <use-async-echarts-and-detail :loading="false" :logo="averageDistributionAmount.logo" :options="averageDistributionAmount.options" height="250px" />
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts
              width="100%"
              height="300px"
              :loading="percentageOfDisadvantagedStudents.loading"
              :options="percentageOfDisadvantagedStudents.options" />
            <use-async-echarts width="100%" height="300px" :loading="averageFinancialAidRatio.loading" :options="averageFinancialAidRatio.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="2" tab="困难生" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">困难生分布情况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select></div
            >
          </div>
          <div class="two-column-grid-with-gap">
            <use-async-echarts-and-detail :loading="false" :logo="studentDisadvantageType.logo" :options="studentDisadvantageType.options" height="250px" />
            <use-async-echarts-and-detail :loading="false" :logo="departmentDistribution.logo" :options="departmentDistribution.options" height="250px" />
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts width="100%" height="300px" :loading="studentDisadvantageTypeChange.loading" :options="studentDisadvantageTypeChange.options" />
            <use-async-echarts width="100%" height="300px" :loading="departmentDistributionChange.loading" :options="departmentDistributionChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="3" tab="助学金" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">助学金概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="zxjgk">
            <div class="zxjgk_item zxjgk_item1">
              <img src="../image/zzqk_zxjzl.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">助学金种类</p>
              </div>
            </div>
            <div class="zxjgk_item zxjgk_item2">
              <img src="../image/zzqk_szrc.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">受助人次</p>
              </div>
            </div>
            <div class="zxjgk_item zxjgk_item3">
              <img src="../image/zzqk_zxjzje.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">助学金总金额(万)</p>
              </div>
            </div>
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts width="100%" height="400px" :loading="scholarshipStudentCountChange.loading" :options="scholarshipStudentCountChange.options" />
            <use-async-echarts width="100%" height="400px" :loading="scholarshipAmountChange.loading" :options="scholarshipAmountChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="4" tab="勤工助学" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">勤工助学概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="two-column-grid-with-gap">
            <use-async-echarts-and-detail
              :loading="false"
              viewType="tb"
              :logo="employedStudentsDisadvantagedCount.logo"
              :options="employedStudentsDisadvantagedCount.options"
              height="270px" />
            <use-async-echarts-and-detail
              :loading="false"
              viewType="tb"
              :logo="disadvantagedStudentsPerUnit.logo"
              :options="disadvantagedStudentsPerUnit.options"
              height="270px" />
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <!-- 受助学生人次变化 -->
            <use-async-echarts width="100%" height="400px" :loading="beneficiaryStudentCountChange.loading" :options="beneficiaryStudentCountChange.options" />
            <!-- 困难生占比变化 -->
            <use-async-echarts
              width="100%"
              height="400px"
              :loading="disadvantagedStudentRatioChange.loading"
              :options="disadvantagedStudentRatioChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="5" tab="贷款" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">贷款概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select></div
            >
          </div>
          <div class="edge-module">
            <div class="edge-module_item edge-module_item1">
              <div>
                <p class="value">0.00</p>
                <p class="name">校园地贷款总金额（万元）</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item2">
              <div>
                <p class="value">0</p>
                <p class="name">校园地贷款总人次</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item3">
              <div>
                <p class="value">0.00</p>
                <p class="name">生源地贷款总金额（万元）</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item4">
              <div>
                <p class="value">0</p>
                <p class="name">生源地贷款总人次</p>
              </div>
            </div>
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts width="100%" height="400px" :loading="totalLoanAmountChange.loading" :options="totalLoanAmountChange.options" />
            <use-async-echarts width="100%" height="400px" :loading="totalLoanBorrowerCountChange.loading" :options="totalLoanBorrowerCountChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="6" tab="困难生" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">困难生概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="knbz">
            <div class="knbz_item knbz_item1">
              <img src="../image/zzqk_knbzzl.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">困难生种类</p>
              </div>
            </div>
            <div class="knbz_item knbz_item2">
              <img src="../image/zzqk_knbzszr.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">受助人次</p>
              </div>
            </div>
            <div class="knbz_item knbz_item3">
              <img src="../image/zzqk_knbzze.png" alt="" />
              <div>
                <p class="value">0</p>
                <p class="name">困难生总金额(万)</p>
              </div>
            </div>
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts width="100%" height="400px" :loading="scholarshipStudentCountChange.loading" :options="scholarshipStudentCountChange.options" />
            <use-async-echarts width="100%" height="400px" :loading="scholarshipAmountChange.loading" :options="scholarshipAmountChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
      <a-tab-pane key="7" tab="代偿" class="h-610px">
        <ScrollContainer>
          <div class="card-herder pl0">
            <div class="title">代偿概况</div>
            <div>
              <a-select v-model:value="selectValue" style="width: 160px">
                <a-select-option value="1">2024-2025年</a-select-option>
                <a-select-option value="2">2023-2024年</a-select-option>
                <a-select-option value="3" disabled>2022-2023年</a-select-option>
              </a-select></div
            >
          </div>
          <div class="edge-module">
            <div class="edge-module_item edge-module_item1">
              <div>
                <p class="value">0.00</p>
                <p class="name">基层就业代偿总金额（万元）</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item2">
              <div>
                <p class="value">0</p>
                <p class="name">基层就业代偿总人次</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item3">
              <div>
                <p class="value">0.00</p>
                <p class="name">义务兵代偿总金额（万元）</p>
              </div>
            </div>
            <div class="edge-module_item edge-module_item4">
              <div>
                <p class="value">0</p>
                <p class="name">义务兵代偿总人次</p>
              </div>
            </div>
          </div>
          <div class="card-herder pl0 mt-20px">
            <div class="title">近三年变化情况</div>
            <div></div>
          </div>
          <div class="lineChange">
            <use-async-echarts width="100%" height="400px" :loading="totalRepaymentAmountChange.loading" :options="totalRepaymentAmountChange.options" />
            <use-async-echarts
              width="100%"
              height="400px"
              :loading="totalRepaymentBorrowerCountChange.loading"
              :options="totalRepaymentBorrowerCountChange.options" />
          </div>
        </ScrollContainer>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, unref, onMounted, nextTick } from 'vue';
  import { RangePicker } from 'ant-design-vue';
  import { ScrollContainer } from '@/components/Container';
  import useAsyncEcharts from './useAsyncEcharts.vue';
  import useAsyncEchartsAndDetail from './useAsyncEchartsAndDetail.vue';
  import zzqk_knsrs from '../image/zzqk_knsrs.png';
  import zzqk_knsrjzzje from '../image/zzqk_knsrjzzje.png';
  import zzqk_knlxfb from '../image/zzqk_knlxfb.png';
  import zzqk_xyzd from '../image/zzqk_xyzd.png';
  import zzqk_qgzxknsnews from '../image/zzqk_qgzxknsnews.png';
  import zzqk_gdwkns from '../image/zzqk_gdwkns.png';

  const activeKey = ref('1');
  const selectValue = ref('1');

  //#region 学生资助情况

  // #region 资助工作概述
  const disadvantagedStudents = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_knsrs,
      value: '10',
      name: '困难生人数',
    },
    options: {
      color: ['#40D5EC', '#9D7FFE'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0px 4px 24px 0px rgba(84, 97, 120, 0.2)',
      },
      legend: {
        show: true,
        y: 'bottom',
        x: 'center',
        padding: [20, 0, 0, 0],
        icon: 'circle',
      },
      series: [
        {
          type: 'pie',
          radius: ['48%', '73%'],
          center: ['50%', '50%'],
          itemStyle: {
            normal: {
              borderWidth: 3,
              borderColor: '#fff',
            },
          },
          data: [
            {
              value: 10,
              name: '困难生',
              label: {
                normal: {
                  show: true,
                  formatter: '\n{a|{d}%}\n\n{b|困难生占比}\n',
                  rich: {
                    a: {
                      fontSize: 24,
                      color: '#131D3C',
                      align: 'center',
                      fontWeight: 600,
                    },
                    b: {
                      color: '#717992',
                      fontSize: 14,
                      align: 'center',
                    },
                  },
                },
              },
            },
            {
              value: 4002,
              name: '普通学生',
              label: {
                normal: {
                  show: false,
                  formatter: '{d}%\n{c}人\n',
                  textStyle: {
                    fontSize: 25,
                  },
                },
              },
            },
          ],
          label: {
            normal: {
              show: false,
              position: 'center',
            },
          },
          labelLine: {
            normal: {
              show: true,
            },
            emphasis: {
              show: false,
            },
          },
          minAngle: 5,
        },
      ],
    },
  });
  // 人均发放金额
  const averageDistributionAmount = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_knsrs,
      value: '0',
      name: '人均发放金额',
    },
    options: {
      title: {
        align: 'left',
        left: '0',
        top: '-5',
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
        show: true,
        textStyle: {
          color: '#333',
        },
        axisPointer: {
          type: 'none',
        },
      },
      legend: {
        show: false,
        data: '',
        x: 'right',
        padding: [5, 0, 0, 0],
      },
      xAxis: [
        {
          type: 'category',
          data: ['0-1k', '1-2k', '2-3k', '3-4k', '4-5k', '5-6k', '6-7k', '7-8k', '8k以上'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            color: '#717992',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        nameLoaction: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      dataZoom: [
        {
          show: true,
          height: 12,
          xAxisIndex: [0],
          bottom: '0%',
          startValue: 0,
          endValue: 4,
          handleStyle: {
            color: '#C5CBD8',
          },
          textStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zoomLock: true,
          realtime: true,
          borderColor: '#fff',
          fillerColor: '#C5CBD8',
          dataBackground: {
            lineStyle: {
              color: '#fff',
            },
            areaStyle: {
              color: '#fff',
            },
          },
        },
      ],
      grid: {
        left: '2%',
        right: '3%',
        bottom: '6%',
        containLabel: true,
      },
      series: [
        {
          name: '',
          color: '#FFC143',
          type: 'bar',
          barMaxWidth: 16,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: [10, 0, 0, 0, 0, 0, 0, 0, 0],
          barMinHeight: 3,
        },
      ],
    },
  });

  // 困难生占比情况
  const percentageOfDisadvantagedStudents = reactive({
    loading: false,
    options: {
      title: {
        text: '困难生占比变化',
        align: 'left',
        left: '0',
        subtext: '单位（%）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['人次占比'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '人次占比',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['7.69', '0', '.25'],
        },
      ],
    },
  });
  // 人均资助占比情况
  const averageFinancialAidRatio = reactive({
    loading: false,
    options: {
      title: {
        text: '人均资助金额变化',
        align: 'left',
        left: '0',
        subtext: '单位（元）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['金额数目'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '金额数目',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['75', '0', '0'],
        },
      ],
    },
  });
  //#endregion

  //#region 困难生
  // 困难生类型
  const studentDisadvantageType = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_knlxfb,
      value: '一般困难最多',
      name: '困难生类型分布',
    },
    options: {
      title: {
        align: 'left',
        left: '0',
        top: '-5',
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
        show: true,
        textStyle: {
          color: '#333',
        },
        axisPointer: {
          type: 'none',
        },
      },
      legend: {
        show: false,
        data: '',
        x: 'right',
        padding: [5, 0, 0, 0],
      },
      xAxis: [
        {
          type: 'category',
          data: ['一般困难', '特别困难', '困难', '不困难'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            color: '#717992',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        nameLoaction: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      dataZoom: [
        {
          show: false,
          height: 12,
          xAxisIndex: [0],
          bottom: '0%',
          startValue: 0,
          endValue: 4,
          handleStyle: {
            color: '#C5CBD8',
          },
          textStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zoomLock: true,
          realtime: true,
          borderColor: '#fff',
          fillerColor: '#C5CBD8',
          dataBackground: {
            lineStyle: {
              color: '#fff',
            },
            areaStyle: {
              color: '#fff',
            },
          },
        },
      ],
      grid: {
        left: '2%',
        right: '3%',
        bottom: '6%',
        containLabel: true,
      },
      series: [
        {
          name: '',
          color: '#4671FD',
          type: 'bar',
          barMaxWidth: 32,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['5', '3', '2', '0'],
          barMinHeight: 3,
        },
      ],
    },
  });
  // 院系分布
  const departmentDistribution = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_xyzd,
      value: '新闻传播学院最多',
      name: '院系分布',
    },
    options: {
      title: {
        align: 'left',
        left: '0',
        top: '-5',
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
        show: true,
        textStyle: {
          color: '#333',
        },
        axisPointer: {
          type: 'none',
        },
      },
      legend: {
        show: true,
        data: ['特别困难', '困难', '一般困难', '不困难'],
        x: 'right',
        padding: [5, 0, 0, 0],
      },
      xAxis: [
        {
          type: 'category',
          data: [
            '新闻传播学院',
            '人文社会科学学院',
            '体育学院',
            '信息科学技术学院',
            '公共管理学院',
            '冶金与化学工程系',
            '医科学院',
            '历史学院',
            '外国语学院',
            '工程管理学院',
            '建筑与信息工程系',
            '文学院',
            '法学院',
            '物理学院',
            '现代工程与应用科学学院',
            '生命科学学院',
            '经济管理学院',
            '资源与环境科学学院',
            '软件学院',
            '金融学院',
            '马克思主义学院',
          ],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            color: '#717992',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        nameLoaction: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      dataZoom: [
        {
          show: true,
          height: 12,
          xAxisIndex: [0],
          bottom: '0%',
          startValue: 0,
          endValue: 4,
          handleStyle: {
            color: '#C5CBD8',
          },
          textStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zoomLock: true,
          realtime: true,
          borderColor: '#fff',
          fillerColor: '#C5CBD8',
          dataBackground: {
            lineStyle: {
              color: '#fff',
            },
            areaStyle: {
              color: '#fff',
            },
          },
        },
      ],
      grid: {
        left: '2%',
        right: '3%',
        bottom: '6%',
        containLabel: true,
      },
      series: [
        {
          name: '特别困难',
          color: '#4671FD',
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['3', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
        {
          name: '困难',
          color: '#FFC143',
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['2', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
        {
          name: '一般困难',
          color: '#40D5EC',
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#40D5EC',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['5', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
        {
          name: '不困难',
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 16,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
      ],
    },
  });
  // 困难生类型 人数变化
  const studentDisadvantageTypeChange = reactive({
    loading: false,
    options: {
      title: {
        text: '',
        align: 'left',
        left: '0',
        subtext: '单位（人）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['一般困难', '困难', '特别困难', '不困难'],
        x: 'right',
        top: 5,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '一般困难',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['776', '0', '5'],
        },
        {
          name: '困难',
          color: '#9D7FFE',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#9D7FFE',
            },
          },
          data: ['1', '0', '2'],
        },
        {
          name: '特别困难',
          color: '#40D5EC',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#40D5EC',
            },
          },
          data: ['2', '0', '3'],
        },
        {
          name: '不困难',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['0', '0', '0'],
        },
      ],
    },
  });
  //  院系分布 人数变化
  const departmentDistributionChange = reactive({
    loading: false,
    options: {
      title: {
        subtext: '单位（人）',
        align: 'left',
        left: '0',
        top: '-5',
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
        show: true,
        textStyle: {
          color: '#333',
        },
        axisPointer: {
          type: 'none',
        },
      },
      legend: {
        show: true,
        data: ['2023年', '2024年', '2025年'],
        x: 'right',
        padding: [5, 0, 0, 0],
      },
      xAxis: [
        {
          type: 'category',
          data: [
            '新闻传播学院',
            '现代工程与应用科学学院',
            '法学院',
            '医科学院',
            '经济管理学院',
            '公共管理学院',
            '外国语学院',
            '历史学院',
            '资源与环境科学学院',
            '信息科学技术学院',
            '文学院',
            '物理学院',
            '金融学院',
            '马克思主义学院',
            '软件学院',
            '工程管理学院',
            '体育学院',
            '生命科学学院',
            '人文社会科学学院',
            '建筑与信息工程系',
            '冶金与化学工程系',
          ],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            color: '#717992',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        nameLoaction: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      dataZoom: [
        {
          show: true,
          height: 12,
          xAxisIndex: [0],
          bottom: '0%',
          startValue: 0,
          endValue: 4,
          handleStyle: {
            color: '#C5CBD8',
          },
          textStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zoomLock: true,
          realtime: true,
          borderColor: '#fff',
          fillerColor: '#C5CBD8',
          dataBackground: {
            lineStyle: {
              color: '#fff',
            },
            areaStyle: {
              color: '#fff',
            },
          },
        },
      ],
      grid: {
        left: '2%',
        right: '3%',
        bottom: '6%',
        containLabel: true,
      },
      series: [
        {
          name: '2023年',
          color: '#4671FD',
          type: 'bar',
          barMaxWidth: 12,
          barMinWidth: 12,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['38', '116', '66', '77', '75', '79', '19', '2', '0', '0', '0', '99', '0', '0', '0', '89', '0', '119', '0', '0', '0'],
          barMinHeight: 3,
        },
        {
          name: '2024年',
          color: '#FFC143',
          type: 'bar',
          barMaxWidth: 12,
          barMinWidth: 12,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
        {
          name: '2025年',
          color: '#40D5EC',
          type: 'bar',
          barMaxWidth: 12,
          barMinWidth: 12,
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#40D5EC',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: ['10', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'],
          barMinHeight: 3,
        },
      ],
    },
  });

  //#endregion

  //#region 助学金
  const scholarship = reactive({
    // 助学金种类
    TypeOfScholarship: 0,
    // 受助人次
    NumberOfBeneficiaries: 0,
    // 助学金总金额(万)
    Amount: 0,
  });

  // 受助学生人次变化
  const scholarshipStudentCountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '受助学生人次变化',
        align: 'left',
        left: '0',
        subtext: '单位（人次）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['人次数目'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '人次数目',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['118', '0', '0'],
        },
      ],
    },
  });
  // 受助总金额变化
  const scholarshipAmountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '受助总金额变化',
        align: 'left',
        left: '0',
        subtext: '单位（万元）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['金额数目'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '金额数目',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [5.9, 0, 0],
        },
      ],
    },
  });
  // #endregion

  //#region 勤工助学
  // 上岗学生困难生人数
  const employedStudentsDisadvantagedCount = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_qgzxknsnews,
      value: '0',
      name: '上岗学生困难生人数',
    },
    options: {
      color: ['#40D5EC', '#4671FD'],
      legend: {
        show: true,
        padding: [20, 0, 0, 0],
        right: '0',
        bottom: 0,
        icon: 'circle',
        orient: 'vertical',
      },
      series: [
        {
          type: 'pie',
          radius: ['48%', '73%'],
          center: ['50%', '50%'],
          itemStyle: {
            normal: {
              borderWidth: 3,
              borderColor: '#ffffff',
            },
          },
          data: [
            {
              value: 0,
              name: '困难生',
            },
            {
              value: 0,
              name: '非困难生',
            },
          ],
          label: {
            formatter: '{per|{c}人}\n{b|{b}}  ',
            borderWidth: 40,
            borderRadius: 4,
            padding: [0, -20],
            textAlign: 'left',
            rich: {
              b: {
                color: '#2B3145',
                lineHeight: 20,
                fontSize: 14,
                align: 'center',
              },
              per: {
                color: '#2B3145',
                align: 'center',
                fontSize: 14,
                lineHeight: 20,
              },
            },
          },
          labelLine: {
            normal: {
              length: 20,
              length2: 20,
            },
          },
          minAngle: 0,
        },
        {
          type: 'pie',
          radius: ['48%', '73%'],
          center: ['50%', '50%'],
          roseType: 'radius',
          itemStyle: {
            normal: {
              borderWidth: 3,
              borderColor: '#ffffff',
            },
          },
          data: [
            {
              value: 0,
              name: '困难生',
              label: {
                normal: {
                  show: true,
                  position: 'center',
                  formatter: '\n{a|{d}%}\n\n{b|困难生占比}\n',
                  rich: {
                    a: {
                      fontSize: 24,
                      color: '#131D3C',
                      align: 'center',
                      fontWeight: 600,
                    },
                    b: {
                      color: '#717992',
                      fontSize: 14,
                      align: 'center',
                    },
                  },
                },
              },
            },
            {
              value: 0,
              name: '非困难生',
            },
          ],
          label: {
            normal: {
              show: false,
            },
          },
          minAngle: 0,
        },
      ],
    },
  });
  // 各单位困难生
  const disadvantagedStudentsPerUnit = reactive({
    loading: false,
    logo: {
      imgSrc: zzqk_gdwkns,
      value: '暂无困难生人数最多的单位',
      name: '各单位困难生',
    },
    options: {
      title: {
        subtext: '单位（人）',
        left: '0',
        top: '0',
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
        show: true,
        textStyle: {
          color: '#333',
        },
        axisPointer: {
          type: 'none',
        },
      },
      legend: {
        show: false,
        data: 'true',
        x: 'right',
      },
      xAxis: [
        {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            color: '#717992',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        nameLoaction: 'left',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      dataZoom: [
        {
          show: false,
          height: 12,
          xAxisIndex: [0],
          bottom: '0%',
          startValue: 0,
          endValue: 4,
          handleStyle: {
            color: '#C5CBD8',
          },
          textStyle: {
            color: 'rgba(0,0,0,0)',
          },
          zoomLock: true,
          realtime: true,
          borderColor: '#fff',
          fillerColor: '#C5CBD8',
          dataBackground: {
            lineStyle: {
              color: '#fff',
            },
            areaStyle: {
              color: '#fff',
            },
          },
        },
      ],
      grid: {
        left: '2%',
        right: '3%',
        bottom: '6%',
        containLabel: true,
      },
      series: [
        {
          name: 't',
          color: '#04BDAA',
          type: 'bar',
          barMaxWidth: '20',
          barMinWidth: '16',
          barGap: 0,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#04BDAA',
              barBorderRadius: [5, 5, 0, 0],
            },
          },
          data: [],
          barMinHeight: 3,
        },
      ],
    },
  });

  // 困难生占比变化
  const disadvantagedStudentRatioChange = reactive({
    loading: false,
    options: {
      title: {
        text: '困难生占比变化',
        align: 'left',
        left: '0',
        subtext: '单位（%）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['所占比例'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '所占比例',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [14.29, 0, 0],
        },
      ],
    },
  });
  // 受助学生人次变化
  const beneficiaryStudentCountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '受助学生人次变化',
        align: 'left',
        left: '0',
        subtext: '单位（人次）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['上岗学生人次', '困难生人次'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '上岗学生人次',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: ['19', '0', '0'],
        },
        {
          name: '困难生人次',
          color: '#FFC143',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
            },
          },
          data: ['3', '0', '0'],
        },
      ],
    },
  });

  //#endregion

  //#region 贷款
  // 贷款总金额变化
  const totalLoanAmountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '贷款总金额变化',
        align: 'left',
        left: '0',
        subtext: '单位（万元）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['校园地贷款', '生源地贷款'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '校园地贷款',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [0, 0, 0],
        },
        {
          name: '生源地贷款',
          color: '#FFC143',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
            },
          },
          data: [0, 0, 0],
        },
      ],
    },
  });
  //  贷款总人次变化
  const totalLoanBorrowerCountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '贷款总人次变化',
        align: 'left',
        left: '0',
        subtext: '单位（人次）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['校园地贷款', '生源地贷款'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '校园地贷款',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [0, 0, 0],
        },
        {
          name: '生源地贷款',
          color: '#FFC143',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
            },
          },
          data: [0, 0, 0],
        },
      ],
    },
  });

  // #endregion

  //#region 代偿
  // 代偿总金额变化
  const totalRepaymentAmountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '代偿总金额变化',
        align: 'left',
        left: '0',
        subtext: '单位（万元）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['基层就业代偿', '义务兵代偿'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '基层就业代偿',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [0, 0, 0],
        },
        {
          name: '义务兵代偿',
          color: '#FFC143',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
            },
          },
          data: [0, 0, 0],
        },
      ],
    },
  });
  //  代偿总人次变化
  const totalRepaymentBorrowerCountChange = reactive({
    loading: false,
    options: {
      title: {
        text: '代偿总人次变化',
        align: 'left',
        left: '0',
        subtext: '单位（人次）',
        textStyle: {
          fontSize: 14,
          color: '#808695',
          fontWeight: 600,
        },
      },
      legend: {
        show: true,
        data: ['基层就业代偿', '义务兵代偿'],
        x: 'right',
        top: 20,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        extraCssText: 'box-shadow: 0 3px 10px 0 rgba(84,97,120,0.20);',
      },
      xAxis: [
        {
          type: 'category',
          data: ['2023年', '2024年', '2025年'],
          axisLine: {
            lineStyle: {
              color: '#c7c7c7',
            },
          },
          axisLabel: {
            show: true,
            color: '#717992',
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: {
        name: '',
        nameLoaction: 'middle',
        splitNumber: 5,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#999999',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#999999',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E8E8E8',
          },
        },
      },
      grid: {
        left: '1%',
        right: '0%',
        bottom: '5%',
        containLabel: true,
      },
      series: [
        {
          name: '基层就业代偿',
          color: '#4671FD',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'left',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#4671FD',
            },
          },
          data: [0, 0, 0],
        },
        {
          name: '义务兵代偿',
          color: '#FFC143',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              label: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#999999',
                },
              },
              color: '#FFC143',
            },
          },
          data: [0, 0, 0],
        },
      ],
    },
  });

  // #endregion

  //#endregion
</script>

<style lang="less" scoped>
  @import '../css/common.less';

  // 助学金
  .zxjgk {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 35px;
    margin-bottom: 15px;
    .zxjgk_item {
      border-radius: 10px;
      position: relative;
      display: flex;
      align-items: center;
      img {
        display: block;
        width: 60px;
        height: 60px;
        margin: 20px 15px 20px;
      }
      .value {
        font-size: 32px;
        font-weight: 900;
        line-height: 32px;
        color: rgba(255, 255, 255, 1);
        letter-spacing: 1px;
      }
      .name {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
        margin-top: 2px;
      }
    }
    .zxjgk_item1 {
      background: #4671fd url('../image/zxj_qs.png') no-repeat right center;
    }
    .zxjgk_item2 {
      background: #63cedf url('../image/zxj_qs.png') no-repeat right center;
    }
    .zxjgk_item3 {
      background: #9d7ffe url('../image/zxj_popu.png') no-repeat right center;
    }
  }

  // 贷款
  // 困难生情况
  .knbz {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 35px;
    margin-bottom: 15px;
    .knbz_item {
      border-radius: 10px;
      position: relative;
      display: flex;
      align-items: center;
      img {
        display: block;
        width: 60px;
        height: 60px;
        margin: 20px 15px 20px;
      }
      .value {
        font-size: 32px;
        font-weight: 900;
        line-height: 32px;
        color: rgba(255, 255, 255, 1);
        letter-spacing: 1px;
      }
      .name {
        font-size: 16px;
        color: rgba(255, 255, 255, 1);
        margin-top: 2px;
      }
    }
    .knbz_item1 {
      background: #04bdaa url('../image/knbz_qs.png') no-repeat right center;
    }
    .knbz_item2 {
      background: #ffc143 url('../image/knbz_yell.png') no-repeat right center;
    }
    .knbz_item3 {
      background: #ff6860 url('../image/knbz_red.png') no-repeat right center;
    }
  }
</style>
