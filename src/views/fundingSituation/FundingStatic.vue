<!--
 * @Description: 资助详情
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" cancel-text="关闭" title="资助详情">
    <div class="p-20px">
      <!-- 顶部卡片 -->

      <a-row :gutter="24">
        <!-- 左侧学生信息 -->
        <a-col :span="6">
          <a-card class="mb-20px">
            <div class="point mb-10px">学生信息</div>
            <div class="student-info-card">
              <a-avatar :size="100" shape="square" src="" />
              <div>
                <div class="student-name">张三 </div>
                <div class="student-desc mt-6px">2018级 - 现代工程与应用</div>
                <div class="student-desc mt-6px">能源科学工程-能源与环境</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="18">
          <a-card class="mb-20px">
            <div class="funding-status-header mb-18px">
              <div class="point">2019-2020学年资助情况</div>
              <Badge status="processing" text="困难生等级：非困难生" />
            </div>
            <a-slider :min="0" :max="100" />
            <div class="funding-status-content">
              <div class="funding-status-item">
                <div class="funding-status-label">资助目标(元)</div>
                <div class="funding-status-value">无</div>
              </div>
              <div class="funding-status-item">
                <div class="funding-status-label">已资助(元)</div>
                <div class="funding-status-value">1200</div>
              </div>
              <div class="funding-status-item">
                <div class="funding-status-label">差额(元)</div>
                <div class="funding-status-value">0</div>
              </div>
              <div class="funding-status-item">
                <div class="funding-status-label">在校期间累计受资助金额</div>
                <div class="funding-status-value">1700元</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 资助明细和分布 -->
      <a-card class="mb-20px">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="point mb-10px">资助明细</div>
            <div class="funding-detail-list">
              <template v-for="item in fundingDetails">
                <div class="funding-detail-item">
                  <div class="funding-detail-type">
                    <img src="./images/jl.png" />
                    <span class="funding-detail-label">{{ item.type }}</span>
                    <div class="funding-detail-amount ml-10px">¥ {{ item.amount }}</div>
                  </div>
                  <div class="funding-detail-desc">{{ item.desc }}</div>
                </div>
              </template>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="point mb-10px">资助分布</div>
            <div class="funding-pie-chart">
              <Chart :options="pieOption" :height="180" />
              <!-- <div class="pie-chart-label">奖学金</div> -->
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 近半年消费情况 -->
      <a-card>
        <div class="point">近半年消费情况</div>
        <Chart :options="lineOption" :height="220" />
      </a-card>
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Badge } from 'ant-design-vue';
  import Chart from '@/views/statisticalInquiry/components/chart.vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  const [registerPopup, {}] = usePopupInner(() => {});
  const fundingDetails = ref([
    { type: '奖学金', amount: 1200, desc: '简单奖学金-普通优秀奖学金，不可叠奖' },
    { type: '助学金', amount: 500, desc: '国家助学金' },
    { type: '临时补助', amount: 300, desc: '临时困难生' },
    { type: '其他补助', amount: 3000, desc: '其他补助' },
    { type: '奖学金', amount: 1200, desc: '简单奖学金-普通优秀奖学金，不可叠奖' },
    { type: '助学金', amount: 500, desc: '国家助学金' },
    { type: '临时补助', amount: 300, desc: '临时困难生' },
    { type: '其他补助', amount: 3000, desc: '其他补助' },
  ]);

  const pieOption = ref({
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '资助分布',
        type: 'pie',
        radius: ['50%', '80%'],
        data: fundingDetails.value.map(item => ({ value: item.amount, name: item.type })),
      },
    ],
  });
  const lineOption = ref({
    tooltip: { trigger: 'axis' },
    grid: { left: 40, right: 20, top: 30, bottom: 30 },
    legend: {
      data: ['学生消费情况', '学生平均消费情况'],
    },
    xAxis: {
      type: 'category',
      data: ['2019-05', '2019-06', '2019-07', '2019-08', '2019-09', '2019-10'],
      boundaryGap: false,
      splitLine: { show: true },
    },
    yAxis: {
      type: 'value',
      splitLine: { show: true },
    },
    series: [
      {
        name: '学生消费情况',
        type: 'line',
        data: [0, 130, 0, 0, 0, 0],
        areaStyle: { color: 'rgba(24,144,255,0.15)' },
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { color: '#1890ff' },
        itemStyle: { color: '#1890ff' },
      },
      {
        name: '学生平均消费情况',
        type: 'line',
        data: [0, 10, 0, 0, 0, 0],
        symbol: 'circle',
        lineStyle: { color: 'green' },
      },
    ],
  });
</script>
<style scoped lang="less">
  .student-info-card {
    display: flex;
    gap: 20px;
    align-items: center;

    .student-name {
      font-size: 18px;
      font-weight: bold;
    }

    .student-desc {
      color: #888;
      font-size: 13px;
      margin-top: 6px;
    }
  }

  .funding-status-content {
    display: flex;
    margin-top: 24px;
    gap: 32px;

    .funding-status-item {
      flex: 1;

      .funding-status-label {
        color: #888;
        font-size: 13px;
        margin-bottom: 4px;
      }

      .funding-status-value {
        font-size: 20px;
        font-weight: bold;
        color: #1890ff;
      }
    }
  }
  .funding-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .funding-detail-list {
    height: 200px;
    overflow-y: auto;
  }

  .funding-detail-item {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    background: #f8fafc;
    border-radius: 8px;
    padding: 10px 24px;
    box-shadow: 0 1px 4px #f0f1f2;
    margin-bottom: 10px;

    .funding-detail-type {
      display: flex;
      align-items: center;
      img {
        width: 20px;
        height: 20px;
      }
      .funding-detail-label {
        margin-left: 6px;
        width: 100px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .funding-detail-amount {
      font-weight: bold;
      margin-right: 30px;
      width: 80px;
    }

    .funding-detail-desc {
      color: #888;
      font-size: 13px;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
