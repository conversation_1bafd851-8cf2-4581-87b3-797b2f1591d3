<!--
 * @Description: 
 * @Autor: Fhz
 * @Date: 2025-04-24 15:42:28
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 14:37:21
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();

  // 初始化API
  const api = useBaseApi('/api/kns/jt/knlx');

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'jtknlxmc',
        label: '家庭困难类型名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入家庭困难类型名称', trigger: 'blur', type: 'string' }],
      },
      {
        field: 'jtknlxsm',
        label: '家庭困难类型说明',
        component: 'Textarea',
      },
      {
        field: 'sfsy',
        label: '是否使用',
        component: 'Switch',
        defaultValue: 1,
        rules: [{ required: true, message: '请选择是否使用' }],
      },
      {
        field: 'sortCode',
        label: '排序',
        component: 'InputNumber',
        defaultValue: 0,
        rules: [{ required: true, message: '请输入排序' }],
      },
    ],
    labelWidth: 140,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');

  const getTitle = computed(() => (id.value ? '编辑困难类型' : '新增困难类型'));

  async function init(data) {
    changeLoading(true);
    resetFields();
    id.value = data.id || '';

    if (id.value) {
      try {
        const { data: detailData } = await api.request('get', `/${id.value}`);

        // 数据转换处理
        const formData = {
          jtknlxmc: detailData.jtknlxmc || '',
          jtknlxsm: detailData.jtknlxsm || '',
          sfsy: Number(detailData.sfsy || 0),
          sortCode: Number(detailData.sortCode || 0),
        };

        setFieldsValue(formData);
      } catch (error) {
        console.error('获取详情失败:', error);
        createMessage.error('获取详情失败');
      }
    }
    changeLoading(false);
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    changeOkLoading(true);

    try {
      // 数据转换处理
      const submitData = {
        ...values,
        id: id.value || undefined,
        sfsy: values.sfsy ? '1' : '0', // 转换为字符串
        sortCode: Number(values.sortCode || 0),
      };

      let result;
      if (id.value) {
        // 编辑
        result = await api.request('put', `/edit/${id.value}`, {
          data: submitData,
        });
      } else {
        // 新增
        result = await api.request('post', '/save', {
          data: submitData,
        });
      }

      createMessage.success(result.msg || '保存成功');
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      changeOkLoading(false);
    }
  }
</script>
