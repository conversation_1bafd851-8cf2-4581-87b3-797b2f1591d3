<template>
  <ScrollContainer ref="wrapperRef" v-loading="loading">
    <div class="pages">
      <BasicForm @register="registerForm">
        <template #flowAduit>
          <FlowAudit templatedId="679244695498412101" layOut="horizontal"></FlowAudit>
        </template>
        <template #xjgsksrq="{ model, field }">
          <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </template>
        <template #yjgsksrq="{ model, field }">
          <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </template>
        <template #bjgsksrq="{ model, field }">
          <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </template>
        <template #startTime="{ model, field }">
          <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </template>
      </BasicForm>
      <div class="anchorGroup">
        <div
          v-for="(item, index) in anchorLinks"
          :key="item.href"
          :class="['anchorGroup-item', anchorKey == item.href ? 'anchorActive' : '']"
          @click="handleClickAnchor(item.href)">
          {{ item.title }}
        </div>
      </div>
    </div>
    <!-- 悬浮保存按钮 -->
    <div class="floating-save-btn">
      <a-button type="primary" @click="handleSubmit" :loading="loading" size="large"> 保存设置 </a-button>
    </div>
  </ScrollContainer>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { ScrollContainer } from '@/components/Container';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getKnsSzOne, saveKnsSz, updateKnsSz } from '@/api/stuDifficult/knsSz';
  import { useDebounceFn } from '@vueuse/core';
  import FlowAudit from '@/views/common/flowAudit/index.vue';
  import dayjs from 'dayjs';
  import { RangePicker } from 'ant-design-vue';
  const baseStore = useBaseStore();
  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: [
      {
        field: 'form-a1',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a1', content: '申请时间' },
      },
      {
        field: 'xn',
        label: '学年',
        component: 'Select',
        componentProps: { options: [], placeholder: '请选择' },
        rules: [{ required: true, message: '请选择学年' }],
      },
      {
        field: 'dateRange',
        label: '申请时间',
        component: 'Input',
        slot: 'startTime',
        componentProps: {
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        field: 'form-a2',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a2', content: '审核截止时间' },
      },
      {
        field: 'shjzsj',
        label: '院系审核截止日期',
        component: 'DatePicker',
        componentProps: { picker: 'date', placeholder: '请选择日期' },
      },
      {
        field: 'form-a3',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a3', content: '公示信息' },
      },
      {
        field: 'xjgs',
        label: '校级公示',
        defaultValue: 0,
        component: 'Switch',
        componentProps: {},
      },
      {
        field: 'xjgsksrq',
        label: '校级公示日期',
        component: 'Input',
        ifShow: ({ values }) => values.xjgs == true,
        rules: [{ required: true, message: '请选择日期', type: 'array' }],
        slot: 'xjgsksrq',
      },

      {
        field: 'yjgs',
        label: '学院公示',
        defaultValue: 0,
        component: 'Switch',
        componentProps: {},
      },
      {
        field: 'yjgsksrq',
        label: '学院公示日期',
        component: 'Input',
        ifShow: ({ values }) => values.yjgs == true,
        slot: 'yjgsksrq',
        rules: [{ required: true, message: '请选择日期', type: 'array' }],
      },

      {
        field: 'bjgs',
        label: '班级公示',
        component: 'Switch',
        defaultValue: 0,
        componentProps: {},
      },
      {
        field: 'bjgsksrq',
        label: '班级公示日期',
        component: 'Input',
        ifShow: ({ values }) => values.bjgs == true,
        rules: [{ required: true, message: '请选择日期', type: 'array' }],
        slot: 'bjgsksrq',
      },

      {
        field: 'form-a4',
        label: '',
        component: 'GroupTitle',
        componentProps: { id: 'form-a4', content: '审核流程设置' },
      },
      {
        field: 'flowAduit',
        label: '',
        component: 'Input',
        slot: 'flowAduit',
      },
    ],
    labelWidth: 130,
  });
  const loading = ref(false);
  const id = ref('');
  const { createMessage } = useMessage();
  const { t } = useI18n();

  const getTitle = computed(() => (!unref(id) ? '新建' : '编辑'));

  async function getOptions() {
    const academicYears = await baseStore.getDictionaryData('xn');
    updateSchema({ field: 'xn', componentProps: { options: academicYears, fieldNames: { label: 'fullName', value: 'enCode' } } });
  }

  async function initData() {
    loading.value = true;
    try {
      const res = await getKnsSzOne();
      if (res.data) {
        // 处理公示日期数据，将开始和结束时间合并为数组
        const formData = { ...res.data };
        // 校级公示日期
        if (formData.sqksrq && formData.sqjsrq) {
          formData.dateRange = [formData.sqksrq, formData.sqjsrq];
        }

        // 校级公示日期
        if (formData.xjgsksrq && formData.xjgsjsrq) {
          formData.xjgsksrq = [formData.xjgsksrq, formData.xjgsjsrq];
        }

        // 院级公示日期
        if (formData.yjgsksrq && formData.yjgsjsrq) {
          formData.yjgsksrq = [formData.yjgsksrq, formData.yjgsjsrq];
        }

        // 班级公示日期
        if (formData.bjgsksrq && formData.bjgsjsrq) {
          formData.bjgsksrq = [formData.bjgsksrq, formData.bjgsjsrq];
        }
        formData.xjgs = Number(formData.xjgs);
        formData.bjgs = Number(formData.bjgs);
        formData.yjgs = Number(formData.yjgs);
        setFieldsValue(formData);
        id.value = res.data.id;
      }
    } catch (error) {
      console.error('获取设置数据失败:', error);
    } finally {
      loading.value = false;
    }
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    loading.value = true;
    try {
      const query = { ...values, id: id.value == ''?undefined:id.value };

      // 申请时间日期范围
      if (query.dateRange && Array.isArray(query.dateRange)) {
        const xjgsRange = query.dateRange;
        query.sqksrq = dayjs(xjgsRange[0]).format('YYYY-MM-DD 00:00:00');
        query.sqjsrq = dayjs(xjgsRange[1]).format('YYYY-MM-DD 23:59:59');
      }
      // 校级公示日期
      if (query.xjgsksrq && Array.isArray(query.xjgsksrq)) {
        const xjgsRange = query.xjgsksrq;
        query.xjgsksrq = dayjs(xjgsRange[0]).format('YYYY-MM-DD 00:00:00');
        query.xjgsjsrq = dayjs(xjgsRange[1]).format('YYYY-MM-DD 23:59:59');
      }

      // 院级公示日期
      if (query.yjgsksrq && Array.isArray(query.yjgsksrq)) {
        const yjgsRange = query.yjgsksrq;
        query.yjgsksrq = dayjs(yjgsRange[0]).format('YYYY-MM-DD 00:00:00');
        query.yjgsjsrq = dayjs(yjgsRange[1]).format('YYYY-MM-DD 23:59:59');
      }

      // 班级公示日期
      if (query.bjgsksrq && Array.isArray(query.bjgsksrq)) {
        const bjgsRange = query.bjgsksrq;
        query.bjgsksrq = dayjs(bjgsRange[0]).format('YYYY-MM-DD 00:00:00');
        query.bjgsjsrq = dayjs(bjgsRange[1]).format('YYYY-MM-DD 23:59:59');
      }

      const formMethod = id.value ? updateKnsSz : saveKnsSz;
      const res = await formMethod(query);
      createMessage.success(res.msg || '保存成功');
      await initData(); // 重新加载数据
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      loading.value = false;
    }
  }
  const anchorLinks = [
    { href: 'form-a1', title: '申请时间' },
    { href: 'form-a2', title: '审核截止时间' },
    { href: 'form-a3', title: '公示信息' },
    { href: 'form-a4', title: '审核流程设置' },
    // { href: 'form-a5', title: '申请表打印模板' },
    // { href: 'form-a6', title: '学生申请表打印流程' },
  ];
  const anchorKey = ref('form-a1');
  const wrapperRef = ref<ComponentRef>(null);
  const anchor = ref(null);
  let isClickTriggered = false;
  let debounceTimer = null; // 防抖定时器

  const getContainer = () => {
    return wrapperRef.value.$el;
  };
  const handleClickAnchor = link => {
    isClickTriggered = true;
    anchorKey.value = link;
    const element = document.getElementById(link);
    element && element.scrollIntoView({ behavior: 'smooth' });
    setTimeout(() => {
      isClickTriggered = false;
    }, 300);
  };

  const observer = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const groupId = entry.target.getAttribute('id');
          if (!isClickTriggered) {
            if (debounceTimer) {
              clearTimeout(debounceTimer);
            }
            debounceTimer = setTimeout(() => {
              anchorKey.value = groupId;
            }, 200);
          }
        }
      });
    },
    {
      root: wrapperRef.value,
      rootMargin: '0px',
      threshold: 0.5,
    },
  );

  const groupTitles = ref();
  onMounted(() => {
    setTimeout(() => {
      groupTitles.value = document.querySelectorAll('[id^="form-a"]');
      groupTitles.value.forEach(title => {
        observer.observe(title);
      });
    }, 10);

    // 初始化数据
    getOptions();
    initData();
  });

  onUnmounted(() => {
    groupTitles.value.forEach(title => {
      observer.unobserve(title);
    });
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
  });
  defineExpose({
    handleSubmit,
  });
</script>
<style scoped lang="less">
  .pages {
    width: 90%;
    min-width: 600px;
    padding-bottom: 100px;
    overflow: hidden;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 200px;
    grid-gap: 20px;
  }
  .floating-save-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px 0;
    text-align: center;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);

    .ant-btn {
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(22, 119, 255, 0.3);
      }
    }
  }

  .anchorGroup {
    position: fixed;
    right: 0;
    padding: 20px;

    transition: color 0.3s;
    .anchorGroup-item {
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
      border-left: 2px solid #f0f0f0;
      cursor: pointer;
    }
    .anchorActive {
      color: @primary-color;
      border-left: 2px solid @primary-color;
    }
  }

  .h-100px {
    height: 100px;
  }
</style>
