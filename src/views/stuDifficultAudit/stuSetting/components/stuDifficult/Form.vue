<!--
 * @Description: 
 * @Autor: Fhz
 * @Date: 2025-04-24 15:42:28
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 15:54:44
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const api = useBaseApi('/api/knsLx');
  const { createMessage } = useMessage();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'knlxmc',
        label: '类型名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        ifShow: () => type.value != 2,
      },
      {
        field: 'knlxmc',
        label: '等级名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入等级名称', trigger: 'blur' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'zzmb',
        label: '资助目标',
        component: 'Input',
      },
      {
        field: 'zzmeblxs',
        label: '资助名额比例系数',
        component: 'InputNumber',
      },
      {
        field: 'rjsrzx',
        label: '年人均收入最小',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最小值', trigger: 'blur' }],
        ifShow: () => type.value == 1,
      },
      {
        field: 'rjsrzd',
        label: '年人均收入最大',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最大值', trigger: 'blur' }],
        ifShow: () => type.value == 1,
      },
      {
        field: 'zxwjdf',
        label: '问卷得分范围下限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围下限', trigger: 'blur' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'zdwjdf',
        label: '问卷得分范围上限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围上限', trigger: 'blur' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'sfsy',
        label: '是否使用',
        component: 'Switch',
        defaultValue: 1,
        rules: [{ required: true, message: '请选择是否使用', trigger: 'change' }],
      },
    ],
    labelWidth: 140,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');

  const getTitle = computed(() => (type.value == 2 ? '困难生类型' : '困难生类型'));
  const type = ref('');

  async function init(data) {
    changeLoading(true);
    resetFields();
    type.value = data.type;
    id.value = data.id;

    try {
      if (id.value) {
        // 获取详情数据
        const { data: detail } = await api.request('get', `/${id.value}`);

        // 数据转换处理 - 字段名已与API一致，只需处理数据类型
        const formData = {
          knlxmc: detail.knlxmc, // 类型名称/等级名称
          zzmb: detail.zzmb, // 资助目标
          zzmeblxs: Number(detail.zzmeblxs || 0), // 资助名额比例系数
          rjsrzx: detail.rjsrzx, // 年人均收入最小
          rjsrzd: detail.rjsrzd, // 年人均收入最大
          zxwjdf: detail.zxwjdf, // 最小问卷得分
          zdwjdf: detail.zdwjdf, // 最大问卷得分
          sfsy: Number(detail.sfsy || 0), // 是否使用
        };

        setFieldsValue(formData);
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      createMessage.error('获取详情失败');
    } finally {
      changeLoading(false);
    }
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    changeOkLoading(true);

    try {
      // 数据转换处理 - 字段名已与API一致，只需处理数据类型
      const submitData = {
        id: id.value,
        knlxmc: values.knlxmc, // 类型名称/等级名称
        zzmb: values.zzmb, // 资助目标
        zzmeblxs: String(values.zzmeblxs || ''), // 资助名额比例系数
        rjsrzx: values.rjsrzx, // 年人均收入最小
        rjsrzd: values.rjsrzd, // 年人均收入最大
        zxwjdf: values.zxwjdf, // 最小问卷得分
        zdwjdf: values.zdwjdf, // 最大问卷得分
        sfsy: values.sfsy ? '1' : '0', // 是否使用
      };

      // 根据是否有ID判断新增还是编辑
      const url = id.value ? `/edit/${id.value}` : '/save';
      const method = id.value ? 'put' : 'post';

      const { msg } = await api.request(method, url, {
        data: submitData,
      });

      createMessage.success(msg || '保存成功');
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      changeOkLoading(false);
    }
  }
</script>
