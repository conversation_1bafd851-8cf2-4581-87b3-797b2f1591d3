<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 17:39:55
-->
<template>
  <Drawer v-bind="$attrs" title="填写统计" width="600" @close="handleOk">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'count'">
          <span @click="handleOk" class="count-color">{{ record.count }}</span>
        </template>
      </template>
    </BasicTable>
    <template #footer>
      <a-button type="primary" @click="handleOk">关闭</a-button>
    </template>
  </Drawer>
</template>
<script lang="ts" setup>
  import { watch } from 'vue';
  import { Drawer } from 'ant-design-vue';
  import { BasicTable, useTable, BasicColumn } from '@/components/Table';
  import { useBaseStore } from '@/store/modules/base';
  import * as schoolApi from '@/api/school';

  // 定义 props
  const props = defineProps<{
    visible: boolean;
  }>();
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/knsMzpy');
  const statColumns: BasicColumn[] = [
    { title: '民主评议单位', dataIndex: 'unit', resizable: true, ellipsis: true, width: 200 },
    { title: '填写数量', dataIndex: 'count', resizable: true, ellipsis: true, width: 100, align: 'right' },
  ];

  const statData = [{ unit: '能源与矿业工程学院', count: 1 }];
  const baseStore = useBaseStore();

  const [registerTable, { getForm }] = useTable({
    api: params => api.request('get', '/api/knsMzpy/statisticsList', { params, isFullPath: true }),
    columns: statColumns,
    dataSource: statData,
    showIndexColumn: true,
    showTableSetting: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 120,
      schemas: [
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
            fieldNames: { label: 'dwBzmc', value: 'dwDm' },
          },
        },
        {
          field: 'xn',
          label: '学年',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
            fieldNames: { label: 'fullName', value: 'enCode' },
          },
        },
      ],
    },
  });
  const emits = defineEmits(['handleOk']);
  const handleOk = record => {
    emits('handleOk', record);
  };

  // 加载选项数据
  async function getOptions() {
    const form = getForm();
    // 加载学院数据
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      form.updateSchema({ field: 'xy', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
    });

    // 加载学年数据
    const academicYears = await baseStore.getDictionaryData('xn');
    form.updateSchema({ field: 'xn', componentProps: { options: academicYears, fieldNames: { label: 'fullName', value: 'enCode' } } });
  }

  // 监听 visible 属性变化
  watch(
    () => props.visible,
    newVisible => {
      if (newVisible) {
        // Drawer 打开时，延迟加载数据确保表单已渲染
        setTimeout(() => {
          getOptions();
        }, 100);
      }
    },
  );

  // Drawer 可见性变化时的处理（备用方案）
</script>
<style scoped lang="less">
  .count-color {
    color: @primary-color;
    cursor: pointer;
  }
</style>
