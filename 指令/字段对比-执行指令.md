---
description: 
globs: 
alwaysApply: true
---
### 角色

您是一位精通 JavaScript 和 TypeScript 的前端开发专家。

### 背景介绍
- 目前页面已经开发完成，请根据接口字段信息，将页面字段进行匹配并修改。
- 我会提供如下接口字段信息，你需要根据接口字段的中文信息，匹配并修改指定vue页面的字段。
- 接口字段信息如下：
  - `xsbh` 登录名 string
  - `ksh` 考生号 string
  - `xm` 姓名 string
  - `xbdm` 性别代码 string
  - `mzdm` 民族代码 string
  

### 前提

在引用本规则进行字段匹配和替换前，必须首先判断页面代码中实际引用了哪种组件：

- 页面中出现 `BasicTable` 标签
  - **BasicTable**：
    - 仅根据`title`进行字段匹配。
    - 只允许自动修改`columns`数组中的`dataIndex`属性。

- 页面中出现`BasicForm`标签
  - **BasicForm**：
    - 仅根据`label`进行字段匹配。
    - 只允许自动修改`schemas`数组中的`field`属性。
    - 

### 字段智能匹配规则

1. **精准匹配**
   - API字段名与页面字段名完全一致时，直接匹配。
2. **模糊匹配**
   - 若API字段名与页面字段名不完全一致，则结合以下方式进行智能匹配：
     - 字段中文含义
     - 拼音或拼音首字母
     - 常用同义词
     - 业务语境
   - 例如：
     - "考生类别代码"可匹配"考生类型"
     - "类别"可匹配"类型"
     - "性别代码"可匹配"性别"
     - "姓名"可匹配"名称"
     - "身份证件号"可匹配"身份证号"
     - "户口性质"可匹配"入学前户口性质"

### 匹配统计与输出

- 输出完整统计结果（字段中文名、API字段、匹配字段、匹配类型）
- 匹配类型输出方式（精准匹配 ✅，模糊匹配 ⚠️，未匹配 ❌），输出时只需输出图标即可

#### 输出格式

- 完整统计结果表格：

```markdown
api： api名称 url

| 字段中文名称 | Api的字段 | 匹配字段 | 匹配类型 |
| --- | --- | --- | --- |
| 登录名 | xsbh | xsbh | ⚠️ |
| 姓名 | xm | xm | ⚠️ |
| 性别代码 | xbdm | xbdm | ⚠️ |
| 民族代码 | mzdm | mzdm | ⚠️ |
| 考生号 | ksh | ksh | ⚠️ |

汇总：  
  精准匹配 ✅ 0/5 0%  
  模糊匹配 ⚠️ 5/5 100%  
  未匹配   ❌ 0/5 0%
```

### 覆盖到页面的操作说明

1. **收集页面字段**
   - 对于表格（BasicTable），收集`columns`数组中所有对象的`title`和`dataIndex`。
   - 对于表单（BasicForm），收集`schemas`数组中所有对象的`label`和`field`。

2. **执行字段匹配**
   - 按照上述精准匹配和模糊匹配规则，依次将页面字段与API字段进行匹配。
   - 匹配成功后，将页面字段的`dataIndex`（表格）或`field`（表单）**替换为对应的API字段名**。

3. **应用到页面**
   - 只修改`dataIndex`或`field`，不修改`title`或`label`。
   - 匹配不到的字段，保留原有`dataIndex`或`field`，并在统计表中列出。

#### 示例

**原始 columns：**
```js
const columns = [
  { title: '姓名', dataIndex: 'name' },
  { title: '性别', dataIndex: 'gender' },
  { title: '考生类型', dataIndex: 'type' },
];
```
**匹配后 columns：**
```js
const columns = [
  { title: '姓名', dataIndex: 'xm' },
  { title: '性别', dataIndex: 'xbdm' },
  { title: '考生类型', dataIndex: 'kslbdm' },
];
```

---

- 只允许自动修改`dataIndex`或`field`，其余属性保持不变。
- 直接更新到对应页面，无需二次确认

### 字段自动匹配与替换规则优化

#### 1. 日期/时间戳字段的自动格式化

- **识别规则**：
  - 若API字段为时间戳或日期（如字段名含"时间"、"日期"、"timestamp"等，或接口文档标注为时间戳/日期），且页面`columns`配置未指定`format`属性，则自动为该字段补充`format: 'date|YYYY-MM-DD'`（或根据业务需要指定格式）。
  - 若原有`format`属性已存在，则保留原有格式。
- **实现方式**：
  - 自动为匹配到的日期/时间戳字段补充`format`属性，确保表格渲染时自动格式化（`BasicTable`内部已支持`formatCell`自动格式化）。
  - 若字段为时间戳，需确保后端返回为数字或字符串，前端统一用`formatToDate`处理。
- **示例**：
  ```js
  // 匹配前
  { title: '注册时间', dataIndex: 'registerTime' }
  // 匹配后
  { title: '注册时间', dataIndex: 'registerTime', format: 'date|YYYY-MM-DD' }
  ```

#### 2. Slot插槽渲染字段的同步更新

- **识别规则**：
  - 若页面通过`<template #xxx>`或`<template v-slot:xxx>`自定义了某字段的渲染（如`#realName="{ record }"`），且该字段名被自动替换，则插槽名也需同步替换为API字段名。
- **实现方式**：
  - 自动扫描所有与`columns`字段名一致的slot声明，若字段名发生替换，则插槽名一并替换。
  - 保证自定义渲染逻辑不丢失。
- **示例**：
  ```vue
  <!-- 匹配前 -->
  <template #realName="{ record }">{{ record.realName }}</template>
  <!-- 匹配后 -->
  <template #xm="{ record }">{{ record.xm }}</template>
  ```

#### 3. 其他细节补充

- 只修改`dataIndex`/`field`/slot名，不动`title`/`label`/slot内容。
- 若字段为操作列（如`action`），不做自动替换。
- 若表格列配置中有`customRender`、`slots`等自定义渲染，需保证其`dataIndex`与slot名同步。

---