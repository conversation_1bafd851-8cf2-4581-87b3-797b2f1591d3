# 接口联调标准化指令

## 概述
本指令用于规范化前后端接口联调流程，通过 Augment 自动化执行接口字段核对、接口编写和测试验证，提高联调效率。

## 执行流程

### 1. 接口信息收集
请按以下格式提供接口信息：

```markdown
## 接口基本信息
- **接口名称**: [接口功能描述]
- **接口路径**: [完整的API路径，如 /api/knsDcwj/getList]
- **请求方法**: [GET/POST/PUT/DELETE]
- **接口前缀**: [API前缀，如 /api/knsDcwj]
- **功能模块**: [所属功能模块，如 questionManage]

## 请求参数
### Query参数 (GET请求)
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNum | number | 否 | 1 | 页码 |
| pageSize | number | 否 | 10 | 每页数量 |

### Body参数 (POST/PUT请求)
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| wjmc | string | 是 | - | 问卷名称 |
| zf | number | 是 | - | 总分数 |

## 响应数据
### 成功响应 (code: 0)
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [],
    "pagination": {
      "total": 0,
      "pageNum": 1,
      "pageSize": 10
    }
  }
}
```

### 错误响应 (code: 非0)
```json
{
  "code": 500,
  "message": "错误信息",
  "data": null
}
```
```

### 2. 自动执行步骤

#### 步骤1: 字段核对
```bash
# Augment 执行指令
1. 检查现有API文件结构
2. 对比接口字段与现有代码中的字段定义
3. 生成字段差异报告
4. 标记需要新增/修改/删除的字段
```

#### 步骤2: 接口文件生成/更新
```bash
# Augment 执行指令
1. 基于 useBaseApi hook 创建/更新接口文件
2. 遵循项目现有的接口定义规范
3. 自动生成 TypeScript 类型定义
4. 添加完整的 JSDoc 注释
```

#### 步骤3: 组件集成
```bash
# Augment 执行指令
1. 在指定的 Vue 组件中集成新接口
2. 更新现有的接口调用
3. 处理响应数据格式转换
4. 添加错误处理逻辑
```

#### 步骤4: 测试验证
```bash
# Augment 执行指令
1. 生成接口测试用例
2. 创建 Mock 数据
3. 验证接口调用逻辑
4. 检查错误处理机制
```

## 代码规范

### 接口文件规范
```typescript
// 文件路径: src/api/[模块名]/[功能名].ts
import { defHttp } from '@/utils/http/axios';
import { useBaseApi } from '@/hooks/web/useBaseApi';

// 使用 useBaseApi Hook (推荐)
export const use[ModuleName]Api = () => {
  return useBaseApi('/api/[prefix]');
};

// 或传统方式 (兼容现有代码)
enum Api {
  Prefix = '/api/[prefix]',
}

/**
 * [接口功能描述]
 * @param data 请求参数
 * @returns Promise<ApiResponse<T>>
 */
export function [functionName](data?: any) {
  return defHttp.[method]({ url: Api.Prefix + '/[path]', data });
}
```

### 组件中使用规范
```vue
<script setup lang="ts">
import { useBaseApi } from '@/hooks/web/useBaseApi';

// 初始化API
const api = useBaseApi('/api/[prefix]');

// 调用接口
const loadData = async () => {
  try {
    const { data } = await api.getList({
      params: {
        pageNum: 1,
        pageSize: 10,
        keyword: searchKeyword.value
      }
    });
    // 处理响应数据
    dataList.value = data.list || [];
  } catch (error) {
    console.error('接口调用失败:', error);
  }
};
</script>
```

### 类型定义规范
```typescript
// 请求参数类型
export interface [ModuleName]QueryParams {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  [key: string]: any;
}

// 响应数据类型
export interface [ModuleName]Item {
  id: string;
  [fieldName]: [fieldType];
  createTime?: string;
  updateTime?: string;
}

// 分页响应类型
export interface [ModuleName]PageResult {
  list: [ModuleName]Item[];
  pagination: {
    total: number;
    pageNum: number;
    pageSize: number;
  };
}
```

## 执行检查清单

### 字段核对检查
- [ ] 请求参数字段完整性检查
- [ ] 响应数据字段完整性检查  
- [ ] 字段类型一致性检查
- [ ] 必填字段验证规则检查
- [ ] 默认值设置检查

### 接口实现检查
- [ ] API路径正确性检查
- [ ] HTTP方法正确性检查
- [ ] 请求参数处理检查
- [ ] 响应数据处理检查
- [ ] 错误处理机制检查

### 代码质量检查
- [ ] TypeScript类型定义完整
- [ ] JSDoc注释完整
- [ ] 代码风格符合项目规范
- [ ] 无语法错误和类型错误
- [ ] 性能优化检查

### 功能测试检查
- [ ] 接口调用成功测试
- [ ] 参数传递正确性测试
- [ ] 响应数据格式验证
- [ ] 错误场景处理测试
- [ ] 边界条件测试

## 常见问题处理

### 字段不匹配
```bash
# 处理方案
1. 确认后端接口文档是否最新
2. 检查字段命名规范是否一致
3. 确认数据类型转换是否正确
4. 添加字段映射处理逻辑
```

### 接口调用失败
```bash
# 排查步骤
1. 检查API路径是否正确
2. 确认请求方法是否匹配
3. 验证请求参数格式
4. 检查网络连接和权限
5. 查看后端日志错误信息
```

### 数据格式问题
```bash
# 解决方案
1. 添加数据格式转换逻辑
2. 统一日期时间格式处理
3. 处理null/undefined值
4. 添加数据验证机制
```

## 使用示例

### 示例1: 创建新接口
```markdown
## 接口信息
- 接口名称: 获取问卷列表
- 接口路径: /api/knsDcwj/getList
- 请求方法: GET
- 接口前缀: /api/knsDcwj
- 功能模块: questionManage

## 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | number | 否 | 页码 |
| pageSize | number | 否 | 每页数量 |
| keyword | string | 否 | 搜索关键词 |

## 响应数据
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": "1",
        "wjmc": "问卷名称",
        "zf": 100,
        "txkssj": "2025-01-01",
        "txjssj": "2025-12-31"
      }
    ],
    "pagination": {
      "total": 1,
      "pageNum": 1,
      "pageSize": 10
    }
  }
}
```

### 示例2: 更新现有接口
```markdown
## 更新说明
- 目标文件: src/views/questionManage/index.vue
- 更新接口: 问卷列表查询接口
- 新增字段: sfqy (是否启用)
- 修改字段: txkssj 改为 startTime
```

## 注意事项

1. **字段命名规范**: 遵循项目现有的字段命名规范，保持一致性
2. **类型安全**: 确保 TypeScript 类型定义完整准确
3. **错误处理**: 添加完善的错误处理和用户提示
4. **性能优化**: 合理使用缓存和防抖机制
5. **文档更新**: 及时更新接口文档和注释

## 执行命令

当您提供接口信息后，请使用以下命令让 Augment 执行联调：

```bash
# 基础联调命令
@augment 根据提供的接口信息，按照 指令/接口联调.md 规范执行接口联调

# 指定目标文件的联调命令  
@augment 根据接口信息更新 [目标文件路径]，按照 指令/接口联调.md 规范执行

# 完整联调命令（包含测试）
@augment 完整执行接口联调流程，包括字段核对、接口编写、组件集成和测试验证
```
